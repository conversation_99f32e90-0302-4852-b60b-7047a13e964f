# app/services/file_processor.py

import os
import pandas as pd
from flask import current_app, session
from .nlp_service import tokenize_korean, tokenize_english, remove_special_characters_and_numbers, DEFAULT_TAGS
from .progress_service import set_progress


def get_file_columns(file_path):
    """Get column names from an Excel file"""
    try:
        data = pd.read_excel(file_path)
        return data.columns.tolist()
    except Exception as e:
        raise Exception(f"Error reading Excel file: {str(e)}")



def read_file(filepath):
    """
    Reads a file (CSV or Excel) and returns a Pandas DataFrame.

    Args:
        filepath (str): The path to the file to be read.

    Returns:
        pd.DataFrame: A DataFrame containing the file's data.

    Raises:
        ValueError: If the file format is unsupported.
        Exception: If there is an error reading the file.
    """
    try:
        if filepath.endswith('.csv'):
            # Read CSV file
            return pd.read_csv(filepath)
        elif filepath.endswith('.xlsx'):
            # Read Excel file with openpyxl engine
            return pd.read_excel(filepath, engine='openpyxl')
        elif filepath.endswith('.xls'):
            # Read older Excel file with xlrd engine
            return pd.read_excel(filepath, engine='xlrd')
        else:
            raise ValueError("Unsupported file format. Only CSV and Excel files are supported.")
    except Exception as e:
        raise Exception(f"Error reading file: {str(e)}")

def process_file(file_path, column_name, options=None, session_id=None):
    """엑셀 파일을 처리하고 결과 파일 경로를 반환"""
    if options is None:
        options = {}

    try:
        # 진행 상황 초기화
        set_progress(0)

        # Split filename and extension
        filename = os.path.splitext(os.path.basename(file_path))[0]

        original_filename = options.get('original_filename', filename)  # Use original filename

        # 엑셀 파일 읽기
        set_progress(10)
        data = read_file(file_path)
        if data.empty:
            return None, "데이터프레임이 비어 있습니다."
        # 열 이름이 비어 있는지 확인
        if column_name not in data.columns:
            return None, f"열 '{column_name}'이 파일에 존재하지 않습니다."

        # 옵션 가져오기
        language = options.get('language', 'korean')
        analyzer_name = options.get('analyzer', 'okt')
        selected_tags = options.get('pos_tags', DEFAULT_TAGS[language])
        min_word_length = int(options.get('min_word_length', 2))
        custom_filename = options.get('custom_filename', '')

        # 데이터 처리
        set_progress(20)
        data[column_name] = data[column_name].astype('str')

        # 토큰화 함수 적용 - 성능 최적화
        set_progress(30)  # 토큰화 시작

        error_rows = []

        # 성능 최적화: 벡터화된 처리 사용
        try:
            if language == 'korean':
                # 벡터화된 한국어 토큰화 처리
                def safe_tokenize_korean(text):
                    try:
                        if pd.isna(text) or text.strip() == '':
                            return "[]"
                        tokens = tokenize_korean(str(text), analyzer_name, selected_tags, min_word_length)
                        return str(tokens)
                    except Exception as e:
                        error_rows.append((len(error_rows), str(e)))
                        return "[]"

                # pandas apply를 사용한 벡터화 처리 (훨씬 빠름)
                data[column_name] = data[column_name].apply(safe_tokenize_korean)

            elif language == 'english':
                # 벡터화된 영어 토큰화 처리
                def safe_tokenize_english(text):
                    try:
                        if pd.isna(text) or text.strip() == '':
                            return "[]"
                        tokens = tokenize_english(str(text), selected_tags, min_word_length)
                        return str(tokens)
                    except Exception as e:
                        error_rows.append((len(error_rows), str(e)))
                        return "[]"

                data[column_name] = data[column_name].apply(safe_tokenize_english)

        except Exception as e:
            # 전체 처리 오류 시 빈 리스트로 설정
            print(f"전체 토큰화 처리 오류: {str(e)}")
            data[column_name] = "[]"

        set_progress(70)  # 토큰화 완료

        # 후처리 작업
        data[column_name] = data[column_name].astype('str')
        data[column_name] = data[column_name].apply(remove_special_characters_and_numbers)
        data[column_name] = data[column_name].astype('str')

        # 결측값이 있는 행 삭제
        data = data.dropna(subset=[column_name])

        # 오류 정보가 있으면 경고 로그 출력
        if error_rows:
            print(f"총 {len(error_rows)}개 행에서 처리 오류 발생. 처음 10개: {error_rows[:10]}")

        set_progress(90)  # 저장 시작

        # 결과 파일 저장
        if custom_filename:
            # 이미 파일 확장자가 있는지 확인
            if custom_filename.lower().endswith('.xlsx'):
                output_filename = custom_filename
            else:
                output_filename = f'{custom_filename}.xlsx'
        else:
            # 품사 태그 문자열 생성 (최대 3개까지만 표시)
            pos_tags_str = '_'.join(selected_tags[:3])
            if len(selected_tags) == 0:
                pos_tags_str = ''
            elif len(selected_tags) > 3:
                pos_tags_str += "_etc"

            # 기본 파일명 생성
            output_filename = f'{original_filename}_{column_name}_{analyzer_name}_{pos_tags_str}_정제_ver1.xlsx'

        # Get upload folder from app config
        result_folder = current_app.config['RESULT_FOLDER']

        # Use provided session_id or fall back to Flask session
        if session_id:
            current_session_id = session_id
        elif 'session_id' in session:
            current_session_id = session['session_id']
        else:
            # If no session ID is available, use a default folder
            current_session_id = 'default'

        session_folder = os.path.join(result_folder, current_session_id)
        if not os.path.exists(session_folder):
            os.makedirs(session_folder)

        output_file = os.path.join(session_folder, output_filename)
        data.to_excel(output_file, index=False)

        # Store output file path in session if using Flask session
        if 'session_id' in session:
            session['output_file'] = output_file

        set_progress(100)  # 모든 작업 완료
        return output_file, None
    except Exception as e:
        set_progress(100)  # 오류 발생 시에도 100%로 표시
        return None, f"처리 중 오류 발생: {str(e)}"


def delete_temp_files(session_id):
    """Delete temporary files for a given session ID"""
    try:
        upload_folder = current_app.config['UPLOAD_FOLDER']
        session_folder = os.path.join(upload_folder, session_id)
        temp_folder = os.path.join(session_folder, 'temp')

        # Delete the temp folder and its contents
        if os.path.exists(temp_folder):
            for filename in os.listdir(temp_folder):
                file_path = os.path.join(temp_folder, filename)
                if os.path.isfile(file_path):
                    os.unlink(file_path)
            os.rmdir(temp_folder)  # Remove the temp folder itself
    except Exception as e:
        print(f"Error deleting temporary files: {str(e)}")




# 폴더 설정
UPLOAD_FOLDER = 'uploads'  # 업로드된 파일 저장 폴더
RESULT_FOLDER = 'results'  # 결과 파일 저장 폴더

os.makedirs(RESULT_FOLDER, exist_ok=True)  # 폴더가 없으면 생성


# ===== 유틸리티 함수 =====
