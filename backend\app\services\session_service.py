"""
Session management utilities for the application.
This module provides functions for managing user sessions consistently across the application.
This is a wrapper around the JSON-based session management system.
"""

import os
from flask import request, current_app
from .json_session_service import (
    get_or_create_session,
    ensure_session_directories,
    get_session_value,
    set_session_value,
    read_session_data
)

def get_or_create_session_id():
    """
    Get the current session ID or create a new one if it doesn't exist.

    Returns:
        str: The session ID
    """
    try:
        # Check if a session_id was provided in the request parameters
        request_session_id = request.args.get('session_id')

        # If a session_id was provided in the request, use it
        if request_session_id:
            # Ensure session directories exist for this ID
            ensure_session_directories(request_session_id)
            print(f"Using provided session ID from request: {request_session_id}")
            return request_session_id

        # Get or create a session
        session_id, _ = get_or_create_session()
        print(f"Using session ID: {session_id}")
        return session_id
    except Exception as e:
        print(f"Error in get_or_create_session_id: {str(e)}")
        # Create a new session as fallback
        session_id, _ = get_or_create_session()
        return session_id

def get_session_status():
    """
    Get the current session status.

    Returns:
        dict: Session status information
    """
    # Try to get session ID from request
    session_id = request.args.get('session_id')

    # If no session ID in request, try to create a new one
    if not session_id:
        session_id, _ = get_or_create_session()

    if not session_id:
        return {
            'active': False,
            'message': 'No active session'
        }

    # Get uploaded file information
    uploaded_file = get_session_value(session_id, 'uploaded_file')

    return {
        'active': True,
        'session_id': session_id,
        'uploaded_file': uploaded_file,
        'has_file': uploaded_file is not None
    }

def get_session_by_id(session_id):
    """
    Get session data for a specific session ID.
    This function allows server-side filtering of sessions by ID.

    Args:
        session_id (str): The session ID to retrieve

    Returns:
        dict: Session information or None if session not found
    """
    # Read session data from JSON file
    session_data = read_session_data(session_id)

    if session_data:
        # Get uploaded file and options from session data
        uploaded_file = get_session_value(session_id, 'uploaded_file')
        options = get_session_value(session_id, 'options')

        return {
            'active': True,
            'session_id': session_id,
            'uploaded_file': uploaded_file,
            'options': options,
            'has_file': uploaded_file is not None
        }

    # If no session data found, check if directories exist
    try:
        # Check if session directories exist for this ID
        upload_folder = current_app.config['UPLOAD_FOLDER']
        result_folder = current_app.config['RESULT_FOLDER']

        session_upload_dir = os.path.join(upload_folder, session_id)
        session_result_dir = os.path.join(result_folder, session_id)

        # If either directory exists, the session is considered valid
        if os.path.exists(session_upload_dir) or os.path.exists(session_result_dir):
            return {
                'active': True,
                'session_id': session_id,
                'has_directories': {
                    'upload': os.path.exists(session_upload_dir),
                    'result': os.path.exists(session_result_dir)
                }
            }

        return None  # Session not found
    except Exception as e:
        print(f"Error in get_session_by_id: {str(e)}")
        return None

def require_session(func):
    """
    Decorator to require an active session for a route.
    If no session exists, a new one will be created.

    Args:
        func: The route function to decorate

    Returns:
        function: The decorated function
    """
    from functools import wraps

    @wraps(func)
    def decorated_function(*args, **kwargs):
        # Ensure session ID exists
        get_or_create_session_id()
        return func(*args, **kwargs)

    return decorated_function
