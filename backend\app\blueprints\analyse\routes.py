# app/blueprints/api/routes.py
from flask import request, jsonify, current_app, url_for
from . import analysis as step3
from ...services.session_service import get_or_create_session_id
from ...services.json_session_service import get_session_value, set_session_value
from ...services.nlp_service import (
        analyze_frequency, create_wordcloud_from_words, get_file_path,
        run_lda_analysis, run_lda_analysis_with_edits,
        create_topic_network, get_updated_topics, update_topic_visualizations,
        create_updated_pyldavis, get_lda_model, read_file)
import os
import json
import pandas as pd
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt

# Configure CORS for React frontend with HTTPS support
@step3.after_request
def after_request(response):
    # Get CORS allowed origins from environment variable or use a default
    cors_allowed_origins = os.getenv('CORS_ALLOWED_ORIGINS', '*')

    # If specific origins are provided, use them
    if cors_allowed_origins != '*':
        # Check if the request origin is in the allowed origins
        request_origin = request.headers.get('Origin')
        if request_origin:
            if ',' in cors_allowed_origins:
                allowed_origins = cors_allowed_origins.split(',')
                if request_origin in allowed_origins:
                    response.headers.add('Access-Control-Allow-Origin', request_origin)
            else:
                if request_origin == cors_allowed_origins:
                    response.headers.add('Access-Control-Allow-Origin', request_origin)
    else:
        # Wildcard for development
        response.headers.add('Access-Control-Allow-Origin', '*')

    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization,X-Requested-With')
    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
    response.headers.add('Access-Control-Allow-Credentials', 'true')

    # Add Vary header to ensure proper caching with CORS
    response.headers.add('Vary', 'Origin')

    return response
# Global variables for LDA model state
global_model = None
global_corpus = None
global_dictionary = None
global_tokens = None
global_edited_keywords = {}


@step3.route('/word_data', methods=['POST'])
def api_get_word_data():
    """단어 목록 데이터만 반환하는 API (수동 선택 모드용)"""
    try:
        # 파일 정보 가져오기
        session_id = request.args.get('session_id')
        if not session_id:
            session_id = get_or_create_session_id()

        filename = get_session_value(session_id, 'uploaded_file')

        column_name = request.form.get('column_name')

        if not session_id or not filename:
            return jsonify({"error": "세션 ID와 파일명이 필요합니다."}), 400

        if not column_name:
            options = get_session_value(session_id, 'options')
            column_name = options.get('column_name') if options else None
            if not column_name:
                return jsonify({"error": "세션 ID와 파일명이 필요합니다."}), 400

        # 파일 경로 생성
        file_path = get_file_path(session_id, filename)

        # 파일 존재 확인
        if not os.path.exists(file_path):
            return jsonify({"error": "파일을 찾을 수 없습니다."}), 404

        # 분석 실행 - 수동 선택 모드로 실행하여 단어 데이터만 반환
        result = analyze_frequency(file_path, column_name, selection_type='manual')

        if "error" in result:
            return jsonify(result), 400

        # Add full download URLs to the result
        if "output_file" in result:
            result["download_url"] = url_for('process.serve_file',
                                            filepath=result["output_file"],
                                            download='true',
                                            session_id=session_id,
                                            _external=True)

        if "wordcloud_file" in result and result["wordcloud_file"]:
            result["wordcloud_url"] = url_for('process.serve_file',
                                             filepath=result["wordcloud_file"],
                                             download='false',
                                             session_id=session_id,
                                             _external=True)

        return jsonify(result)
    except Exception as e:
        # logger.error(f"단어 데이터 처리 오류: {traceback.format_exc()}")
        return jsonify({"error": str(e)}), 500

@step3.route('/analyze', methods=['POST'])
def api_analyze():
    """파일 분석 API"""
    try:
        # 파일 정보 가져오기
        session_id = request.args.get('session_id')
        if not session_id:
            session_id = get_or_create_session_id()

        filename = get_session_value(session_id, 'uploaded_file')

        column_name = request.form.get('column_name')

        if not session_id or not filename:
            return jsonify({"error": "세션 ID와 파일명이 필요합니다."}), 400

        if not column_name:
            options = get_session_value(session_id, 'options')
            column_name = options.get('column_name') if options else None
            if not column_name:
                return jsonify({"error": "세션 ID와 파일명이 필요합니다."}), 400

        # 추가 파라미터
        selection_type = request.form.get('selection_type', 'top_n')
        max_words = request.form.get('max_words', '50')
        cloud_shape = request.form.get('cloud_shape', 'rectangle')
        cloud_color = request.form.get('cloud_color', 'viridis')
        selected_words = request.form.get('selected_words', None)

        # 파일 경로 생성
        file_path = get_file_path(session_id, filename)

        # 파일 존재 확인
        if not os.path.exists(file_path):
            return jsonify({"error": "파일을 찾을 수 없습니다."}), 404

        # 분석 실행
        result = analyze_frequency(
            file_path,
            column_name,
            selection_type,
            max_words,
            cloud_shape,
            cloud_color,
            selected_words
        )

        if "error" in result:
            return jsonify(result), 400

        # Add full download URLs to the result
        if "output_file" in result:
            result["download_url"] = url_for('process.serve_file',
                                            filepath=result["output_file"],
                                            download='true',
                                            session_id=session_id,
                                            _external=True)

        if "wordcloud_file" in result and result["wordcloud_file"]:
            result["wordcloud_url"] = url_for('process.serve_file',
                                             filepath=result["wordcloud_file"],
                                             download='false',
                                             session_id=session_id,
                                             _external=True)

        return jsonify(result)
    except Exception as e:
        # logger.error(f"분석 처리 오류: {traceback.format_exc()}")
        return jsonify({"error": str(e)}), 500

@step3.route('/wordcloud', methods=['POST'])
def api_generate_wordcloud():
    """선택한 단어로 워드클라우드 생성 API"""
    try:
        selected_words = request.form.get('selected_words', '[]')
        cloud_shape = request.form.get('cloud_shape', 'rectangle')
        cloud_color = request.form.get('cloud_color', 'viridis')

        word_list = json.loads(selected_words)

        # 워드클라우드 생성
        session_id = request.args.get('session_id')
        if not session_id:
            session_id = get_or_create_session_id()

        result = create_wordcloud_from_words(word_list, cloud_shape, cloud_color)

        if "error" in result:
            return jsonify(result), 400

        # Add full download URL to the result
        if "wordcloud_file" in result and result["wordcloud_file"]:
            result["wordcloud_url"] = url_for('process.serve_file',
                                             filepath=result["wordcloud_file"],
                                             download='false',
                                             session_id=session_id,
                                             _external=True)

        return jsonify(result)
    except Exception as e:
        # logger.error(f"워드클라우드 생성 오류: {traceback.format_exc()}")
        return jsonify({"error": str(e)}), 500

@step3.route('/edit_words', methods=['POST'])
def api_edit_words():
    """단어 편집 후 워드클라우드 재생성 API"""
    try:
        # Get session info
        session_id = request.args.get('session_id')
        if not session_id:
            session_id = get_or_create_session_id()

        # Get parameters
        edited_words = request.form.get('edited_words', '[]')
        selected_words = request.form.get('selected_words')  # This is now optional
        cloud_shape = request.form.get('cloud_shape', 'rectangle')
        cloud_color = request.form.get('cloud_color', 'viridis')
        column_name = request.form.get('column_name')

        if not column_name:
            options = get_session_value(session_id, 'options')
            column_name = options.get('column_name') if options else None
            if not column_name:
                return jsonify({"error": "Column name is required"}), 400

        try:
            # Parse the edited words JSON
            edited_words_list = json.loads(edited_words)
            if not isinstance(edited_words_list, list):
                return jsonify({"error": "edited_words must be a JSON array"}), 400

            # Get file info
            filename = get_session_value(session_id, 'uploaded_file')
            if not filename:
                return jsonify({"error": "No file found in session"}), 400

            # Get file path
            file_path = get_file_path(session_id, filename)


            # Check if file exists
            if not os.path.exists(file_path):
                return jsonify({"error": "File not found"}), 404


            # Create a mapping of original to edited words
            word_mapping = {item['original'].lower(): item['new'].lower() for item in edited_words_list}
            print(word_mapping)
            print(edited_words_list)
            # Apply the edits to the original word list

            data = read_file(file_path)
            def replace_words(text):
                for old, new in word_mapping.items():
                    text = text.lower().replace(old.lower(), new.lower())
                return text

            data[column_name] = data[column_name].apply(replace_words)
            edited_file_name = f'{filename.split(".")[0]}_edited.csv'
            set_session_value(session_id, 'edited_file', edited_file_name)
            edited_file_path = get_file_path(session_id, edited_file_name)
            data.to_csv(edited_file_path, index=False)
            # Call analyze with the updated words
            result = analyze_frequency(
                edited_file_path,
                column_name,
                max_words=str(data.shape[0]),  # Use the same number of words as input
                cloud_shape=cloud_shape,
                cloud_color=cloud_color,
                selected_words=selected_words  # Pass our edited words
            )

            if "error" in result:
                return jsonify(result), 400

            # Add full download URLs to the result
            if "output_file" in result:
                result["download_url"] = url_for('process.serve_file',
                                                filepath=result["output_file"],
                                                download='true',
                                                session_id=session_id,
                                                _external=True)

            if "wordcloud_file" in result and result["wordcloud_file"]:
                result["wordcloud_url"] = url_for('process.serve_file',
                                                 filepath=result["wordcloud_file"],
                                                 download='false',
                                                 session_id=session_id,
                                                 _external=True)

            return jsonify(result)

        except json.JSONDecodeError:
            return jsonify({"error": "Invalid JSON format in edited_words"}), 400

    except Exception as e:
        return jsonify({"error": str(e)}), 500

def cleanup_temp_files():
    """일정 시간이 지난 임시 파일들을 정리하는 함수"""
    try:
        # 이 함수는 필요에 따라 구현 (예: 특정 시간 이전에 생성된 파일 삭제)
        pass
    except Exception as e:
        return jsonify({"error": str(e)}), 500
        # logger.error(f"임시 파일 정리 중 오류 발생: {e}")


# (4-3) 데이터 처리 및 LDA 분석 수행
@step3.route('/process', methods=['POST'])
def process_data():
    # (4-3-1) 업로드된 파일 존재 확인
    session_id = request.args.get('session_id')
    if not session_id:
        session_id = get_or_create_session_id()

    filename = get_session_value(session_id, 'uploaded_file')
    if not filename:
        return jsonify({'error': '업로드된 파일이 없습니다'})

    # (4-3-2) 분석 파라미터 가져오기
    data = request.get_json()
    text_column = data.get('text_column')  # 텍스트 데이터가 있는 컬럼명
    min_topic = int(data.get('min_topic', 3))  # 최소 토픽 개수
    max_topic = int(data.get('max_topic', 10))  # 최대 토픽 개수
    no_below = int(data.get('no_below', 5))  # 최소 문서 빈도
    no_above = float(data.get('no_above', 0.2))  # 최대 문서 빈도

    # (4-3-3) 네트워크 시각화 스타일 설정
    network_style = data.get('network_style', 'academic')  # 기본값을 논문용 스타일로 변경

    # 차트 스타일 설정 추가
    chart_style = data.get('chart_style', 'default')  # 기본값은 default 스타일

    # (4-3-4) 수동 토픽 수 설정 확인
    manual_topic_number = data.get('manual_topic_number')

    # (4-3-5) 파일 경로 생성 및 존재 확인
    file_path = get_file_path(session_id, filename)

    # 파일 존재 확인
    if not os.path.exists(file_path):
        return jsonify({"error": "파일을 찾을 수 없습니다."}), 404

    # (4-3-6) 편집된 키워드 초기화 (새로운 모델 학습 시)
    global global_edited_keywords
    global_edited_keywords = {}

    # (4-3-7) LDA 분석 실행 - 전체 모드가 기본값
    # 빠른 모드 설정 (기본값: False - 전체 모드)
    fast_mode = data.get('fast_mode', False)

    try:
        results = run_lda_analysis(
            file_path,
            text_column,
            min_topic,
            max_topic,
            no_below,
            no_above,
            network_style,
            manual_topic_number,
            chart_style,  # 차트 스타일 매개변수 추가
            fast_mode     # 빠른 모드 매개변수 추가
        )

        # Add full download URLs to the results
        if 'csv_path' in results:
            results['csv_download_url'] = url_for('process.serve_file',
                                                filepath=results['csv_path'],
                                                download='true',
                                                session_id=session_id,
                                                _external=True)

        if 'network_img_path' in results:
            results['network_img_url'] = url_for('process.serve_file',
                                               filepath=results['network_img_path'],
                                               download='false',
                                               session_id=session_id,
                                               _external=True)

        # Add URLs for topic images
        if 'topic_images' in results and isinstance(results['topic_images'], list):
            for topic_image in results['topic_images']:
                if 'path' in topic_image:
                    topic_image['url'] = url_for('process.serve_file',
                                               filepath=topic_image['path'],
                                               download='false',
                                               session_id=session_id,
                                               _external=True)

        return jsonify(results)
    except Exception as e:
        return jsonify({'error': str(e)})

# (4-4) 키워드 편집 처리
@step3.route('/edit_keywords', methods=['POST'])
def edit_keywords():
    global global_model, global_corpus, global_dictionary, global_tokens, global_edited_keywords

    # (4-4-1) 모델 로드 확인 - get_lda_model 함수 사용
    session_id = request.args.get('session_id')
    global_model, global_corpus, global_dictionary = get_lda_model(session_id)
    # print(global_model, "GLOBAL MODEL")
    if global_model is None:
        return jsonify({'error': '모델이 로드되지 않았습니다. 먼저 데이터를 처리해주세요.'})

    # (4-4-2) 편집 정보 가져오기
    data = request.get_json()
    topic_id = int(data.get('topic_id'))
    edited_words = data.get('edited_words', [])
    removed_words = data.get('removed_words', [])
    chart_style = data.get('chart_style', 'default')

    # (4-4-3) 편집된 키워드 정보 저장 구조 초기화
    if topic_id not in global_edited_keywords:
        global_edited_keywords[topic_id] = {
            'edited': {},
            'removed': []
        }

    # (4-4-4) 편집된 단어 정보 업데이트
    for word_info in edited_words:
        original_word = word_info.get('original')
        new_word = word_info.get('new')
        if original_word and new_word and original_word.lower() != new_word.lower():
            global_edited_keywords[topic_id]['edited'][original_word.lower()] = new_word.lower()

    # (4-4-5) 제거된 단어 정보 업데이트 - 수정된 로직
    for word in removed_words:
        word_lower = word.lower()
        # 제거 목록에 추가
        if word_lower not in global_edited_keywords[topic_id]['removed']:
            global_edited_keywords[topic_id]['removed'].append(word_lower)

        # 만약 이 단어가 이전에 편집되었다면, 편집 정보도 제거
        # 편집된 단어들 중에서 이 단어와 일치하는 것을 찾아서 제거
        edited_dict = global_edited_keywords[topic_id]['edited']
        keys_to_remove = []
        for original_key, edited_value in edited_dict.items():
            # 원본 단어나 편집된 단어가 제거 대상이면 편집 정보 삭제
            if original_key == word_lower or edited_value == word_lower:
                keys_to_remove.append(original_key)

        for key in keys_to_remove:
            del edited_dict[key]

    # (4-4-6) Call run_lda_analysis with skip_training=True to get the same structure as process endpoint
    try:
        # Get the file path for reanalysis (needed for some visualization functions)
        filename = get_session_value(session_id, 'uploaded_file')
        if not filename:
            return jsonify({'error': '업로드된 파일이 없습니다'})

        file_path = get_file_path(session_id, filename)
        if not os.path.exists(file_path):
            return jsonify({"error": "파일을 찾을 수 없습니다."}), 404

        # Call run_lda_analysis but skip training and use existing model with edited keywords
        result = run_lda_analysis_with_edits(
            file_path=file_path,
            existing_model=global_model,
            existing_corpus=global_corpus,
            existing_dictionary=global_dictionary,
            edited_keywords=global_edited_keywords,
            network_style=data.get('network_style', 'academic'),
            chart_style=chart_style,
            session_id=session_id
        )

        # Add download URLs (these are already included in the result from run_lda_analysis_with_edits)
        if 'csv_path' in result:
            result['csv_download_url'] = url_for('process.serve_file',
                                               filepath=result['csv_path'],
                                               download='true',
                                               session_id=session_id,
                                               _external=True)

        if 'network_img_path' in result:
            result['network_img_url'] = url_for('process.serve_file',
                                              filepath=result['network_img_path'],
                                              download='false',
                                              session_id=session_id,
                                              _external=True)

        # Add URLs for topic images
        if 'topic_images' in result and isinstance(result['topic_images'], list):
            for topic_image in result['topic_images']:
                if 'path' in topic_image:
                    topic_image['url'] = url_for('process.serve_file',
                                               filepath=topic_image['path'],
                                               download='false',
                                               session_id=session_id,
                                               _external=True)

        # (4-4-7) 편집 결과 반환
        return jsonify(result)
    except Exception as e:
        return jsonify({'error': str(e)})

# 차트 스타일 변경 엔드포인트 추가
@step3.route('/update_chart_style', methods=['POST'])
def update_chart_style():
    global global_model, global_edited_keywords

    # 새로운 get_lda_model 함수를 사용하여 모델 가져오기
    session_id = request.args.get('session_id')
    global_model, _, _ = get_lda_model(session_id)

    # 모델이 없으면 오류 반환
    if global_model is None:
        return jsonify({'error': '모델이 로드되지 않았습니다. 먼저 데이터를 처리해주세요.'})

    data = request.get_json()
    chart_style = data.get('chart_style', 'default')

    try:
        # 토픽별 시각화 이미지 업데이트
        topic_images = update_topic_visualizations(global_model, global_edited_keywords, chart_style)

        # Get session ID for URLs
        session_id = request.args.get('session_id')
        if not session_id:
            session_id = get_or_create_session_id()

        # Add URLs for topic images
        if topic_images and isinstance(topic_images, list):
            for topic_image in topic_images:
                if 'path' in topic_image:
                    topic_image['url'] = url_for('process.serve_file',
                                               filepath=topic_image['path'],
                                               download='false',
                                               session_id=session_id,
                                               _external=True)

        return jsonify({
            'success': True,
            'topic_images': topic_images,
            'chart_style': chart_style
        })
    except Exception as e:
        # 오류 발생 시 기본 이미지 생성
        result_folder = current_app.config['RESULT_FOLDER']
        session_id = request.args.get('session_id')
        if not session_id:
            session_id = get_or_create_session_id()
        session_folder = os.path.join(result_folder, session_id)
        topic_folder = os.path.join(session_folder, current_app.config['TOPIC_MODELS_FOLDER'])


        try:
            used_topic_num = global_model.num_topics if global_model else 3
            topic_images = []

            for topic_id in range(used_topic_num):
                topic_img_path = os.path.join(topic_folder, f"topic_{topic_id+1}_default.png")

                # 기본 이미지 파일이 없는 경우 생성
                if not os.path.exists(topic_img_path):
                    try:
                        # 이미지 저장을 위한 디렉토리 확인
                        os.makedirs(os.path.dirname(topic_img_path), exist_ok=True)

                        # 매우 간단한 막대 그래프 생성
                        plt.figure(figsize=(10, 6))
                        plt.barh([f'단어 {i+1}' for i in range(5)], [5-i for i in range(5)])
                        plt.title(f'TOPIC {topic_id+1} (기본 이미지)')
                        plt.xlabel('가중치')
                        plt.ylabel('키워드')
                        plt.savefig(topic_img_path)
                        plt.close()
                    except Exception as e2:
                        print(f"기본 토픽 이미지 생성 실패: {str(e2)}")

                topic_images.append({
                    'id': topic_id,
                    'path': topic_img_path
                })

            # Add URLs for topic images
            if topic_images and isinstance(topic_images, list):
                for topic_image in topic_images:
                    if 'path' in topic_image:
                        topic_image['url'] = url_for('process.serve_file',
                                                   filepath=topic_image['path'],
                                                   download='false',
                                                   session_id=session_id,
                                                   _external=True)

            return jsonify({
                'success': True,
                'topic_images': topic_images,
                'chart_style': chart_style,
                'warning': '차트 스타일 변경 중 오류가 발생하여 기본 이미지가 생성되었습니다.'
            })
        except Exception as recovery_error:
            return jsonify({'error': f"차트 스타일 변경 및 복구 시도 실패: {str(e)}, 복구 오류: {str(recovery_error)}"})

@step3.route('/update_topics', methods=['POST'])
def update_topics():
    # Get current session ID
    session_id = request.args.get('session_id')
    if not session_id:
        session_id = get_or_create_session_id()

    # Get edited keywords from request
    data = request.get_json()
    edited_keywords = data.get('edited_keywords', {})

    # Get current model
    global global_model, global_corpus, global_dictionary
    global_model, global_corpus, global_dictionary = get_lda_model()

    if global_model is None:
        return jsonify({'error': 'No LDA model found'})

    try:
        # Update topics with edited keywords
        topics = get_updated_topics(global_model, edited_keywords)

        # Create updated visualizations
        vis_html = create_updated_pyldavis(global_model, global_corpus, global_dictionary, edited_keywords)
        topic_images = update_topic_visualizations(global_model, edited_keywords)

        return jsonify({
            'topics': topics,
            'pyldavis_html': vis_html,
            'topic_images': topic_images
        })
    except Exception as e:
        return jsonify({'error': str(e)})

@step3.route('/update_network', methods=['POST'])
def update_network():
    # Get current session ID
    session_id = request.args.get('session_id')
    if not session_id:
        session_id = get_or_create_session_id()

    # Get edited keywords and style from request
    data = request.get_json()
    edited_keywords = data.get('edited_keywords', {})
    network_style = data.get('network_style', 'academic')

    # Get current model
    global global_model
    global_model, _, _ = get_lda_model()  # We only need the model for network visualization

    if global_model is None:
        return jsonify({'error': 'No LDA model found'})

    try:
        # Create updated network visualization
        network_img_path = create_topic_network(global_model, global_model.num_topics, network_style=network_style, edited_keywords=edited_keywords)

        # Add download URL to the response
        network_img_url = url_for('process.serve_file',
                               filepath=network_img_path,
                               download='false',
                               session_id=session_id,
                               _external=True)

        return jsonify({
            'network_img_path': network_img_path,
            'network_img_url': network_img_url
        })
    except Exception as e:
        return jsonify({'error': str(e)})
