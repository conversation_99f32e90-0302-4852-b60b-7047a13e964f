from flask import Flask, redirect, url_for, jsonify
from datetime import timed<PERSON><PERSON>
from .config import Config
from .extensions import *  # import extensions here
import os
from .blueprints.upload import upload_bp as upload_blueprint
from .blueprints.api import api as api_blueprint
from .blueprints.data_process import process as process_blueprint
from .blueprints.analyse import analysis as analysis_blueprint
from flask_swagger_ui import get_swaggerui_blueprint
from flask_cors import CORS

swaggerui_blueprint = get_swaggerui_blueprint(
    Config.SWAGGER_URL,  # Swagger UI static files will be mapped to '{SWAGGER_URL}/dist/'
    Config.API_URL,
    config={  # Swagger UI config overrides
        'app_name': "Analysis application",
        'withCredentials': True  # Enable sending cookies with requests
    }
)



def create_app(config_object='app.config.Config'):
    # Create the Flask application instance
    app = Flask(__name__)

    # Load configuration
    app.config.from_object(config_object)

    # Get CORS allowed origins from environment variable or use a default


    # Configure CORS with more specific settings for HTTPS support
    CORS(app,
         resources={r"/*": {"origins": app.config['CORS_ORIGINS']}},
         supports_credentials=True,
         allow_headers=["Content-Type", "Authorization", "X-Requested-With"],
         methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
         vary_header=True)
    # Ensure upload, result, and session directories exist
    if not os.path.exists(app.config['UPLOAD_FOLDER']):
        os.makedirs(app.config['UPLOAD_FOLDER'])

    # Create result folder and its subdirectories
    if not os.path.exists(app.config['RESULT_FOLDER']):
        os.makedirs(app.config['RESULT_FOLDER'])

    # Create session folder
    if not os.path.exists(app.config['SESSION_FOLDER']):
        os.makedirs(app.config['SESSION_FOLDER'])

    # Create session folder
    if not os.path.exists(app.config['MODEL_FOLDER']):
        os.makedirs(app.config['MODEL_FOLDER'])
    # Initialize extensions
    # from .extensions import db, celery
    # db.init_app(app)

    # Register blueprints
    app.register_blueprint(upload_blueprint)
    app.register_blueprint(process_blueprint, url_prefix='/api')
    app.register_blueprint(analysis_blueprint, url_prefix='/api/analyse')
    app.register_blueprint(api_blueprint)
    app.register_blueprint(swaggerui_blueprint)

    # Add a root route that redirects to API documentation or returns basic info
    @app.route('/')
    def index():
        """Root endpoint that provides basic application information and links"""
        return jsonify({
            'name': 'Analysis Application',
            'version': '1.0',
            'documentation': url_for('swagger_ui.show', _external=True),
            'api_status': url_for('api.session_status', _external=True),
            'message': 'Welcome to the Analysis Application API'
        })

    return app