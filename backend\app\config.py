import os
import os
from dotenv import load_dotenv, find_dotenv
load_dotenv(find_dotenv())

class Config:
    BASE_DIR = os.path.abspath(os.path.dirname(__file__))
    UPLOAD_FOLDER = os.path.join(BASE_DIR, '..', 'uploads')        # or absolute path like '/tmp/uploads'
    RESULT_FOLDER = os.path.join(BASE_DIR, '..', 'results')        # or absolute path like '/tmp/results'
    MODEL_FOLDER = os.path.join(BASE_DIR, '..', 'models')
    SESSION_FOLDER = os.path.join(BASE_DIR, '..', 'sessions')      # folder for storing session JSON files
    TOPIC_MODELS_FOLDER = 'topic_images'
    WORD_CLOUD_FOLDER = 'word_clouds'
    WORD_FREQ_FOLDER = 'word_freq'
    # FONT_PATH = os.path.join(BASE_DIR, '..', 'static', 'malgun.ttf')  # 폰트 경로
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB limit (optional)
    DEBUG = True
    SECRET_KEY = os.getenv('FLASK_SECRET_KEY')
    SWAGGER_URL = '/api/docs'  # URL for exposing Swagger UI (without trailing '/')
    API_URL = os.getenv('SWAGGER_JSON_URL', "http://localhost:7000/swaggerui")
    FONT_PATH = os.path.join(os.path.dirname(__file__), 'static', 'malgun.ttf') # 워드 클라우드용 폰트 경로
    cors_allowed_origins = os.getenv('CORS_ALLOWED_ORIGINS', '*')
    cors_allowed_origins = os.getenv('CORS_ALLOWED_ORIGINS', '*')
    # If the environment variable contains multiple origins, split them
    if cors_allowed_origins != '*' and ',' in cors_allowed_origins:
        cors_allowed_origins = cors_allowed_origins.split(',')
    CORS_ORIGINS = cors_allowed_origins

