# Use an official Python runtime as a parent image
FROM python:3.12-slim

# Set the working directory in the container
WORKDIR /app

# Copy only the requirements file to leverage Docker cache
COPY requirements.txt /app/

RUN apt-get update && apt-get install -y \
    build-essential \
    gcc \
    python3-dev \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Install Java (needed for spaCy's dependency)

RUN apt-get update && apt-get install -y default-jdk
# Install dependencies

RUN pip install -r requirements.txt
# --no-cache-dir

# Download spaCy English model (this is a one-time setup, so it can be cached)
RUN python -m spacy download en_core_web_sm

# Copy the rest of the application code

COPY ./backend /app/

# Expose the port the app runs on
EXPOSE 7000

# Set environment variables
ENV FLASK_APP=run.py
ENV FLASK_RUN_PORT=7000
ENV FLASK_ENV=production
ENV FLASK_RUN_HOST=0.0.0.0

# Command to run the application
CMD ["flask", "run", "--host=0.0.0.0", "--port=7000"]