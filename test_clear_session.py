#!/usr/bin/env python3
"""
Test script for the clear-session endpoint functionality.
Tests both clearing specific sessions and clearing all sessions.
"""

import requests
import json
import time
import os

# Configuration
BASE_URL = "http://localhost:7000/api"
TEST_SESSION_IDS = ["test123456", "test789abc", "testdefghi"]

def test_session_endpoints():
    """Test all session-related endpoints"""
    print("🧪 Testing Session Management Endpoints")
    print("=" * 50)
    
    # Test 1: List all sessions
    print("\n1️⃣ Testing: List all sessions")
    test_list_sessions()
    
    # Test 2: Create some test sessions (simulate)
    print("\n2️⃣ Testing: Session creation simulation")
    test_create_sessions()
    
    # Test 3: List sessions again to see the new ones
    print("\n3️⃣ Testing: List sessions after creation")
    test_list_sessions()
    
    # Test 4: Clear a specific session
    print("\n4️⃣ Testing: Clear specific session")
    test_clear_specific_session()
    
    # Test 5: List sessions to verify specific deletion
    print("\n5️⃣ Testing: List sessions after specific deletion")
    test_list_sessions()
    
    # Test 6: Clear all sessions
    print("\n6️⃣ Testing: Clear all sessions")
    test_clear_all_sessions()
    
    # Test 7: List sessions to verify all deletion
    print("\n7️⃣ Testing: List sessions after clearing all")
    test_list_sessions()
    
    print("\n✅ All tests completed!")

def test_list_sessions():
    """Test the /sessions endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/sessions")
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success: Found {data.get('total_sessions', 0)} sessions")
            print(f"📊 Total size: {data.get('total_size_mb', 0)} MB")
            
            if data.get('sessions'):
                print("📋 Sessions:")
                for session in data['sessions'][:3]:  # Show first 3
                    print(f"  - {session.get('session_id', 'Unknown')}: {session.get('total_size_bytes', 0)} bytes")
        else:
            print(f"❌ Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")

def test_create_sessions():
    """Simulate session creation by calling session-status endpoint"""
    print("Creating test sessions...")
    
    for session_id in TEST_SESSION_IDS:
        try:
            # Simulate session creation
            response = requests.get(f"{BASE_URL}/session-status", params={'session_id': session_id})
            if response.status_code == 200:
                print(f"✅ Session {session_id}: Created/Found")
            else:
                print(f"⚠️ Session {session_id}: {response.status_code}")
        except Exception as e:
            print(f"❌ Session {session_id}: {str(e)}")

def test_clear_specific_session():
    """Test clearing a specific session"""
    if not TEST_SESSION_IDS:
        print("⚠️ No test sessions to clear")
        return
        
    session_to_clear = TEST_SESSION_IDS[0]
    print(f"Clearing session: {session_to_clear}")
    
    try:
        response = requests.post(f"{BASE_URL}/clear-session", params={'session_id': session_to_clear})
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success: {data.get('message', 'Session cleared')}")
            print(f"📁 Directories removed: {len(data.get('directories_removed', []))}")
        else:
            data = response.json()
            print(f"❌ Error: {data.get('message', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")

def test_clear_all_sessions():
    """Test clearing all sessions"""
    print("Clearing ALL sessions...")
    
    try:
        response = requests.post(f"{BASE_URL}/clear-session", params={'clear_all': 'true'})
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success: {data.get('message', 'All sessions cleared')}")
            print(f"🗑️ Sessions cleared: {data.get('total_sessions_cleared', 0)}")
            print(f"📁 Directories removed: {data.get('total_directories_removed', 0)}")
            
            if data.get('warnings'):
                print(f"⚠️ Warnings: {len(data['warnings'])}")
                for warning in data['warnings'][:3]:  # Show first 3 warnings
                    print(f"  - {warning}")
        else:
            data = response.json()
            print(f"❌ Error: {data.get('message', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Exception: {str(e)}")

def test_error_cases():
    """Test error cases for the clear-session endpoint"""
    print("\n🚨 Testing Error Cases")
    print("=" * 30)
    
    # Test 1: No parameters
    print("\n1️⃣ Testing: No parameters provided")
    try:
        response = requests.post(f"{BASE_URL}/clear-session")
        print(f"Status Code: {response.status_code}")
        data = response.json()
        print(f"Message: {data.get('message', 'No message')}")
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
    
    # Test 2: Invalid session ID
    print("\n2️⃣ Testing: Invalid session ID")
    try:
        response = requests.post(f"{BASE_URL}/clear-session", params={'session_id': 'nonexistent123'})
        print(f"Status Code: {response.status_code}")
        data = response.json()
        print(f"Message: {data.get('message', 'No message')}")
    except Exception as e:
        print(f"❌ Exception: {str(e)}")

def test_endpoint_documentation():
    """Display endpoint usage examples"""
    print("\n📖 Endpoint Usage Examples")
    print("=" * 40)
    
    examples = [
        {
            "description": "List all sessions",
            "method": "GET",
            "url": f"{BASE_URL}/sessions",
            "example": "curl -X GET http://localhost:7000/api/sessions"
        },
        {
            "description": "Clear specific session",
            "method": "POST", 
            "url": f"{BASE_URL}/clear-session?session_id=abc123def4",
            "example": "curl -X POST 'http://localhost:7000/api/clear-session?session_id=abc123def4'"
        },
        {
            "description": "Clear all sessions",
            "method": "POST",
            "url": f"{BASE_URL}/clear-session?clear_all=true", 
            "example": "curl -X POST 'http://localhost:7000/api/clear-session?clear_all=true'"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}️⃣ {example['description']}")
        print(f"   Method: {example['method']}")
        print(f"   URL: {example['url']}")
        print(f"   cURL: {example['example']}")

if __name__ == "__main__":
    print("🚀 Starting Session Management Tests")
    print(f"🌐 Base URL: {BASE_URL}")
    print(f"⏰ Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # Run main tests
        test_session_endpoints()
        
        # Test error cases
        test_error_cases()
        
        # Show documentation
        test_endpoint_documentation()
        
    except KeyboardInterrupt:
        print("\n\n⏹️ Tests interrupted by user")
    except Exception as e:
        print(f"\n\n💥 Unexpected error: {str(e)}")
    
    print(f"\n🏁 Test session completed at {time.strftime('%Y-%m-%d %H:%M:%S')}")
